import type { Metada<PERSON> } from "next";
import { Inter, Cairo } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const cairo = Cairo({
  variable: "--font-cairo",
  subsets: ["arabic", "latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "قيود المحاسبة - أسهل برنامج محاسبي",
  description: "نظام محاسبة متكامل يساعد أصحاب الأعمال في تسهيل كافة العمليات المحاسبية للشركات والمنشآت",
  keywords: "محاسبة, برنامج محاسبي, فواتير, تقارير مالية, إدارة المخزون",
  authors: [{ name: "قيود المحاسبة" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body
        className={`${inter.variable} ${cairo.variable} sf-pro-ar-display antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
