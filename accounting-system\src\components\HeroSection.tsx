'use client'

import Link from 'next/link'

const HeroSection = () => {
  return (
    <section className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-right">
            <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6 arabic-text leading-tight">
              نمو سريع ومستدام
              <br />
              <span className="text-blue-600">مع إدارة مالية فعّالة</span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 arabic-text leading-relaxed">
              نعيد تعريف إدارة العمليات المالية، لنساعدك على التركيز على ماهو أهم من تطوير وابتكار، 
              يمكنك الآن الاستفادة من حلولنا الذكية والمصممة خصيصًا لتصبح إدارة عملياتك المحاسبية والمالية أسهل 
              ولتقترب أكثر من تحقيق النجاح
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
              <Link 
                href="/signup" 
                className="bg-blue-600 text-white px-8 py-4 rounded-xl hover:bg-blue-700 font-semibold text-lg arabic-text transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                اشترك الآن
              </Link>
              <Link 
                href="/trial" 
                className="bg-white text-blue-600 border-2 border-blue-600 px-8 py-4 rounded-xl hover:bg-blue-50 font-semibold text-lg arabic-text transition-all duration-300"
              >
                ابدأ تجربتك المجانية
              </Link>
            </div>

            {/* Features List */}
            <div className="space-y-3 text-gray-600 arabic-text">
              <div className="flex items-center justify-center lg:justify-start space-x-3 rtl:space-x-reverse">
                <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>14 يومًا تجربة مجانية</span>
              </div>
              <div className="flex items-center justify-center lg:justify-start space-x-3 rtl:space-x-reverse">
                <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>بدون بطاقة ائتمان</span>
              </div>
              <div className="flex items-center justify-center lg:justify-start space-x-3 rtl:space-x-reverse">
                <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>بدون تكاليف إضافية</span>
              </div>
            </div>
          </div>

          {/* Hero Image/Illustration */}
          <div className="relative">
            <div className="bg-gradient-to-br from-blue-100 to-blue-200 rounded-3xl p-8 shadow-2xl">
              {/* Dashboard Preview */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                {/* Dashboard Header */}
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="w-8 h-8 bg-blue-600 rounded-lg"></div>
                      <span className="font-semibold text-gray-900 arabic-text">لوحة التحكم</span>
                    </div>
                    <div className="flex space-x-2 rtl:space-x-reverse">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Dashboard Content */}
                <div className="p-6">
                  {/* Stats Cards */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">125,000</div>
                      <div className="text-sm text-gray-600 arabic-text">إجمالي المبيعات</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">45</div>
                      <div className="text-sm text-gray-600 arabic-text">فاتورة جديدة</div>
                    </div>
                  </div>

                  {/* Chart Placeholder */}
                  <div className="bg-gray-50 h-32 rounded-lg flex items-center justify-center">
                    <div className="text-gray-400 arabic-text">مخطط المبيعات</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-4 -right-4 w-20 h-20 bg-yellow-400 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-green-400 rounded-full opacity-20 animate-pulse delay-1000"></div>
          </div>
        </div>

        {/* Certification Badges */}
        <div className="mt-16 text-center">
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 rtl:space-x-reverse">
            <div className="flex items-center space-x-3 rtl:space-x-reverse bg-white px-6 py-3 rounded-lg shadow-md">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-sm font-semibold text-gray-700 arabic-text">معتمد من هيئة الزكاة والضريبة والجمارك</span>
            </div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse bg-white px-6 py-3 rounded-lg shadow-md">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-sm font-semibold text-gray-700 arabic-text">متوافق مع الفاتورة الإلكترونية</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
