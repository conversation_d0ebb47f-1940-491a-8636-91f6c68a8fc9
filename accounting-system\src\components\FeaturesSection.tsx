'use client'

import { useState } from 'react'
import Link from 'next/link'

const FeaturesSection = () => {
  const [activeTab, setActiveTab] = useState(0)

  const features = [
    {
      title: 'حلول ذكية وموثوقة',
      description: 'بخ<PERSON><PERSON>ة تتجاوز 8 سنوات، لدينا الخبرة والمعرفة المدعومة بتجاربنا السابقة لنكون الأقدر على فهم تحدياتك وتلبية احتياجاتك',
      icon: (
        <svg className="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    },
    {
      title: 'مرونة لا حدود لها، إنتاجية لا تتوقف',
      description: 'في المكتب، أو من المنزل، أو في اجتماع مع عملائك. لا يهم! استمتع بمرونة في أي وقت ومن أي مكان.',
      icon: (
        <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      title: 'قيمة مقابل السعر',
      description: 'استمتع بحلول مناسبة تمامًا لميزانيتك دون المساس بالجودة.',
      icon: (
        <svg className="w-12 h-12 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      )
    }
  ]

  const tabs = [
    {
      title: 'الفاتورة الإلكترونية',
      description: 'أصدر فواتير متوافقة مع هيئة الزكاة والضريبة والجمارك وأرسلها لعملائك لتتبع مبيعاتك بدقة.',
      image: '/images/e-invoice.png'
    },
    {
      title: 'إدارة المستخدمين',
      description: 'حدد أدوار ومسؤوليات أفراد فريقك، لتكون قادر على متابعة أنشطة جميع المستخدمين، وتتبع أي تغيرات.',
      image: '/images/user-management.png'
    },
    {
      title: 'التقارير المالية',
      description: 'أصدر التقارير المالية وشاركها مع أصحاب المصلحة والهيئات المختصة. لضمان دقة وتنظيم البيانات والسجلات المالية لشركتك بسهولة.',
      image: '/images/reports.png'
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-6 arabic-text">
            بسّط عملياتك المالية والمحاسبية باستخدام قيود
          </h2>
          <p className="text-xl text-gray-600 arabic-text max-w-3xl mx-auto">
            اكتشف لماذا تعد منتجات وخدمات قيود الأفضل لتبسيط عملياتك وتقليل الأخطاء
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-6 rounded-xl hover:shadow-lg transition-shadow duration-300">
              <div className="flex justify-center mb-6">
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 arabic-text">
                {feature.title}
              </h3>
              <p className="text-gray-600 arabic-text leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Tabbed Features */}
        <div className="bg-gray-50 rounded-3xl p-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div>
              {/* Tab Navigation */}
              <div className="flex flex-col space-y-4 mb-8">
                {tabs.map((tab, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTab(index)}
                    className={`text-right p-4 rounded-lg transition-all duration-300 ${
                      activeTab === index
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'bg-white text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <h4 className="font-bold text-lg arabic-text mb-2">
                      {tab.title}
                    </h4>
                    <p className={`text-sm arabic-text ${
                      activeTab === index ? 'text-blue-100' : 'text-gray-600'
                    }`}>
                      {tab.description}
                    </p>
                  </button>
                ))}
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link 
                  href="/signup" 
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-semibold arabic-text text-center transition-colors"
                >
                  اشترك الآن
                </Link>
                <Link 
                  href="/trial" 
                  className="bg-white text-blue-600 border-2 border-blue-600 px-6 py-3 rounded-lg hover:bg-blue-50 font-semibold arabic-text text-center transition-colors"
                >
                  ابدأ تجربتك المجانية
                </Link>
              </div>
            </div>

            {/* Image/Preview */}
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
                {/* Mock Dashboard */}
                <div className="bg-gradient-to-r from-blue-600 to-blue-700 p-6">
                  <div className="flex items-center justify-between text-white">
                    <h3 className="font-bold text-lg arabic-text">{tabs[activeTab].title}</h3>
                    <div className="flex space-x-2 rtl:space-x-reverse">
                      <div className="w-3 h-3 bg-white bg-opacity-30 rounded-full"></div>
                      <div className="w-3 h-3 bg-white bg-opacity-30 rounded-full"></div>
                      <div className="w-3 h-3 bg-white bg-opacity-30 rounded-full"></div>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  {activeTab === 0 && (
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span className="text-green-600 font-semibold arabic-text">فاتورة #001</span>
                        <span className="text-green-600 font-bold">1,250 ر.س</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span className="text-blue-600 font-semibold arabic-text">فاتورة #002</span>
                        <span className="text-blue-600 font-bold">2,100 ر.س</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                        <span className="text-yellow-600 font-semibold arabic-text">فاتورة #003</span>
                        <span className="text-yellow-600 font-bold">850 ر.س</span>
                      </div>
                    </div>
                  )}
                  
                  {activeTab === 1 && (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 rounded-lg">
                        <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold">أ</span>
                        </div>
                        <div>
                          <div className="font-semibold arabic-text">أحمد محمد</div>
                          <div className="text-sm text-gray-600 arabic-text">مدير مالي</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 rounded-lg">
                        <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold">س</span>
                        </div>
                        <div>
                          <div className="font-semibold arabic-text">سارة أحمد</div>
                          <div className="text-sm text-gray-600 arabic-text">محاسبة</div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {activeTab === 2 && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-blue-50 p-4 rounded-lg text-center">
                          <div className="text-2xl font-bold text-blue-600">125K</div>
                          <div className="text-sm text-gray-600 arabic-text">إجمالي المبيعات</div>
                        </div>
                        <div className="bg-green-50 p-4 rounded-lg text-center">
                          <div className="text-2xl font-bold text-green-600">45</div>
                          <div className="text-sm text-gray-600 arabic-text">فاتورة جديدة</div>
                        </div>
                      </div>
                      <div className="bg-gray-50 h-20 rounded-lg flex items-center justify-center">
                        <span className="text-gray-400 arabic-text">مخطط الأرباح</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection
