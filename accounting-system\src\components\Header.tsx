'use client'

import { useState } from 'react'
import Link from 'next/link'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  const toggleDropdown = (dropdown: string) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown)
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      {/* Top Banner */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white py-2 px-4">
        <div className="container mx-auto text-center text-sm font-medium">
          <span className="arabic-text">تمارا متاحة الآن في قيود 🎉 اشترك في الباقات السنوية وقسّطها عبر أربع دفعات!</span>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">ق</span>
              </div>
              <span className="text-2xl font-bold text-gray-900 arabic-text">قيود المحاسبة</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            {/* منتجات قيود */}
            <div className="relative group">
              <button 
                className="flex items-center space-x-1 rtl:space-x-reverse text-gray-700 hover:text-blue-600 font-semibold arabic-text"
                onClick={() => toggleDropdown('products')}
              >
                <span>منتجات قيود</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {activeDropdown === 'products' && (
                <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-4">
                  <div className="px-4 py-2">
                    <Link href="/accounting" className="block px-4 py-3 hover:bg-gray-50 rounded-lg">
                      <div className="font-semibold text-gray-900 arabic-text">برنامج قيود المحاسبي</div>
                      <div className="text-sm text-gray-600 arabic-text">برنامج محاسبي سحابي شامل</div>
                    </Link>
                    <Link href="/pos" className="block px-4 py-3 hover:bg-gray-50 rounded-lg">
                      <div className="font-semibold text-gray-900 arabic-text">نقاط البيع</div>
                      <div className="text-sm text-gray-600 arabic-text">تطبيق نقاط البيع المتقدم</div>
                    </Link>
                    <Link href="/collection" className="block px-4 py-3 hover:bg-gray-50 rounded-lg">
                      <div className="font-semibold text-gray-900 arabic-text">قيود تحصيل</div>
                      <div className="text-sm text-gray-600 arabic-text">خدمة تحصيل الفواتير</div>
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* التكاملات */}
            <div className="relative group">
              <button 
                className="flex items-center space-x-1 rtl:space-x-reverse text-gray-700 hover:text-blue-600 font-semibold arabic-text"
                onClick={() => toggleDropdown('integrations')}
              >
                <span>التكاملات</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>

            {/* القطاعات */}
            <div className="relative group">
              <button 
                className="flex items-center space-x-1 rtl:space-x-reverse text-gray-700 hover:text-blue-600 font-semibold arabic-text"
                onClick={() => toggleDropdown('sectors')}
              >
                <span>القطاعات</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>

            {/* الأسعار */}
            <Link href="/pricing" className="text-gray-700 hover:text-blue-600 font-semibold arabic-text">
              الأسعار
            </Link>

            {/* الموارد */}
            <div className="relative group">
              <button 
                className="flex items-center space-x-1 rtl:space-x-reverse text-gray-700 hover:text-blue-600 font-semibold arabic-text"
                onClick={() => toggleDropdown('resources')}
              >
                <span>الموارد</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>
          </nav>

          {/* Auth Buttons */}
          <div className="hidden lg:flex items-center space-x-4 rtl:space-x-reverse">
            <Link 
              href="/login" 
              className="text-gray-700 hover:text-blue-600 font-semibold arabic-text"
            >
              تسجيل الدخول
            </Link>
            <Link 
              href="/signup" 
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 font-semibold arabic-text transition-colors"
            >
              ابدأ مجانًا الآن
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button 
            className="lg:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t border-gray-200">
            <div className="space-y-4">
              <Link href="/products" className="block text-gray-700 font-semibold arabic-text">منتجات قيود</Link>
              <Link href="/integrations" className="block text-gray-700 font-semibold arabic-text">التكاملات</Link>
              <Link href="/sectors" className="block text-gray-700 font-semibold arabic-text">القطاعات</Link>
              <Link href="/pricing" className="block text-gray-700 font-semibold arabic-text">الأسعار</Link>
              <Link href="/resources" className="block text-gray-700 font-semibold arabic-text">الموارد</Link>
              <div className="pt-4 border-t border-gray-200">
                <Link href="/login" className="block text-gray-700 font-semibold arabic-text mb-2">تسجيل الدخول</Link>
                <Link href="/signup" className="block bg-blue-600 text-white px-4 py-2 rounded-lg text-center font-semibold arabic-text">
                  ابدأ مجانًا الآن
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
