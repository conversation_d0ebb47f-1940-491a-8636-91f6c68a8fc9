'use client'

import { useEffect, useState } from 'react'

const StatsSection = () => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const element = document.getElementById('stats-section')
    if (element) {
      observer.observe(element)
    }

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [])

  const stats = [
    {
      number: '50',
      suffix: 'مليون +',
      label: 'عملية محاسبية شهرية تتم على قيود',
      color: 'text-blue-600'
    },
    {
      number: '100',
      suffix: 'ألف +',
      label: 'مستخدم شهري',
      color: 'text-green-600'
    },
    {
      number: '50',
      suffix: 'ألف +',
      label: 'عميل راضي',
      color: 'text-purple-600'
    }
  ]

  const companies = [
    { name: 'Ways', logo: 'W' },
    { name: '<PERSON><PERSON><PERSON>', logo: 'ل' },
    { name: '<PERSON><PERSON><PERSON> Albari', logo: 'و' },
    { name: '<PERSON>khoon', logo: 'د' },
    { name: 'Navco', logo: 'ن' },
    { name: 'Badael', logo: 'ب' },
    { name: 'View Water', logo: 'ف' },
  ]

  return (
    <section id="stats-section" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Stats */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className={`text-6xl font-bold ${stat.color} mb-2`}>
                {isVisible && (
                  <CountUpAnimation 
                    target={parseInt(stat.number)} 
                    suffix={stat.suffix}
                  />
                )}
              </div>
              <p className="text-gray-600 arabic-text text-lg font-medium">
                {stat.label}
              </p>
            </div>
          ))}
        </div>

        {/* Trusted Companies */}
        <div className="text-center mb-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 arabic-text">
            يثق بنا أكثر من 50,000 عميل
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-8 items-center">
            {companies.map((company, index) => (
              <div 
                key={index} 
                className="flex flex-col items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-2">
                  <span className="text-2xl font-bold text-gray-600">{company.logo}</span>
                </div>
                <span className="text-sm text-gray-500 font-medium">{company.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Info */}
        <div className="text-center">
          <p className="text-gray-600 arabic-text text-lg">
            انضم إلى آلاف الشركات التي تثق في قيود لإدارة أعمالها المحاسبية
          </p>
        </div>
      </div>
    </section>
  )
}

// Count Up Animation Component
const CountUpAnimation = ({ target, suffix }: { target: number; suffix: string }) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    const duration = 2000 // 2 seconds
    const steps = 60
    const increment = target / steps
    const stepDuration = duration / steps

    let currentCount = 0
    const timer = setInterval(() => {
      currentCount += increment
      if (currentCount >= target) {
        setCount(target)
        clearInterval(timer)
      } else {
        setCount(Math.floor(currentCount))
      }
    }, stepDuration)

    return () => clearInterval(timer)
  }, [target])

  return (
    <span>
      {count} {suffix}
    </span>
  )
}

export default StatsSection
