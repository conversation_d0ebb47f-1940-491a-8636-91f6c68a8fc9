/* SF Pro AR Display Font Family - Using system fonts as fallback */
/* Note: For production, replace with actual SF Pro AR Display font files */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap');

/* Arabic-optimized font stack */
.sf-pro-ar-display {
  font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Tahoma', 'Arial Unicode MS', sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Font weight classes */
.font-regular {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Arabic text optimization */
.arabic-text {
  direction: rtl;
  text-align: right;
  font-family: 'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif;
  letter-spacing: 0.02em;
  line-height: 1.6;
}

/* English text optimization */
.english-text {
  direction: ltr;
  text-align: left;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}
